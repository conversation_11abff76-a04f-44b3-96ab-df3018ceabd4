package io.syrix.config;

import io.syrix.exception.CompanyContextRequiredException;
import io.syrix.exception.SessionRequiredException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * Interceptor to validate that API requests have a valid company session context.
 * This ensures all API endpoints (except auth endpoints) have access to company data.
 */
@Component
public class CompanySessionInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanySessionInterceptor.class);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        logger.debug("Session validation for {} {}", method, requestURI);
        
        // All exclusions are handled by WebMvcConfig - no additional exclusions needed
        
        // Get session without creating a new one
        HttpSession session = request.getSession(false);
        
        if (session == null) {
            logger.warn("No session found for request: {} {}", method, requestURI);
            throw new SessionRequiredException("No active session");
        }
        
        // Check if company ID exists in session
        UUID companyId = (UUID) session.getAttribute("companyId");
        if (companyId == null) {
            logger.warn("No company ID in session for request: {} {}. Session ID: {}", method, requestURI, session.getId());
            throw new CompanyContextRequiredException("No company context in session");
        }
        
        logger.debug("Session validation passed for company: {} on request: {} {}", companyId, method, requestURI);
        return true;
    }
}