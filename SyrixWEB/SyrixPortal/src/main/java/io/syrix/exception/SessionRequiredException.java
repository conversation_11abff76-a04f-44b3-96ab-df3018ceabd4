package io.syrix.exception;

/**
 * Exception thrown when a request requires an active session but none is found.
 * This exception is handled by the global exception handler to provide consistent API responses.
 */
public class SessionRequiredException extends RuntimeException {
    
    /**
     * Constructs a new SessionRequiredException with a default message.
     */
    public SessionRequiredException() {
        super("No active session");
    }
    
    /**
     * Constructs a new SessionRequiredException with the specified message.
     *
     * @param message the detail message
     */
    public SessionRequiredException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new SessionRequiredException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public SessionRequiredException(String message, Throwable cause) {
        super(message, cause);
    }
}