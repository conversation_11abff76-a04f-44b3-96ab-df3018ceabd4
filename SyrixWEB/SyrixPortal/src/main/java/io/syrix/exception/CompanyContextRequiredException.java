package io.syrix.exception;

/**
 * Exception thrown when a request requires a company context in the session but none is found.
 * This exception is handled by the global exception handler to provide consistent API responses.
 */
public class CompanyContextRequiredException extends RuntimeException {
    
    /**
     * Constructs a new CompanyContextRequiredException with a default message.
     */
    public CompanyContextRequiredException() {
        super("No company context in session");
    }
    
    /**
     * Constructs a new CompanyContextRequiredException with the specified message.
     *
     * @param message the detail message
     */
    public CompanyContextRequiredException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new CompanyContextRequiredException with the specified message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public CompanyContextRequiredException(String message, Throwable cause) {
        super(message, cause);
    }
}